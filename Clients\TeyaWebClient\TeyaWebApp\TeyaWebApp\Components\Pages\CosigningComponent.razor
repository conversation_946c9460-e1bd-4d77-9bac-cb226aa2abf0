﻿@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@inject ICosigningService CosigningService
@inject ICosigningCommentHelper CommentHelper
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject ILogger<CosigningComponent> Logger
@inject ISnackbar Snackbar

<MudContainer MaxWidth="MaxWidth.False" Class="cosigning-container">
    @if (ShowCosigningSection)
    {
        <MudPaper Class="cosigning-paper" Elevation="2">
            <!-- Header Section -->
            <MudGrid AlignItems="Center" Justify="Justify.SpaceBetween" Class="cosigning-header">
                <MudItem>
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudIcon Icon="@Icons.Material.Filled.VerifiedUser"
                                 Color="Color.Primary"
                                 Size="Size.Medium" />
                        <MudText Typo="Typo.h6" Class="header-title">
                            @Localizer["DocumentSignature"]
                        </MudText>
                    </MudStack>
                </MudItem>
                <MudItem>
                    <MudChip T="string"
                             Size="Size.Small"
                             Color="@GetStatusColor()"
                             Variant="Variant.Filled"
                             Class="status-chip">
                        @GetStatusText()
                    </MudChip>
                </MudItem>
            </MudGrid>

            <!-- Inline Signing Form (shown when signing is initiated) -->
            @if (_showSigningForm  )
            {
                
              
                <MudPaper Class="signature-form-block" Elevation="2" Style="margin-bottom: 16px; padding: 16px; border: 1px solid #1976d2; background: #f8f9fa;">
                    <MudStack Spacing="3">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.Edit" Color="Color.Primary" Size="Size.Small" />
                            <MudText Typo="Typo.subtitle2" Style="font-weight: 600; color: #1976d2;">
                                @Localizer["Signing"]
                            </MudText>
                            <MudSpacer />
                            <MudIconButton Icon="@Icons.Material.Filled.Close"
                                           Size="Size.Small"
                                           OnClick="CancelSigning"
                                           Color="Color.Error" />
                        </MudStack>

                        <MudDivider />

                        @if (CurrentCosigning.IsSigned)
                        {
                            <!-- Show radio buttons only after document is signed -->
                            <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                                <MudText Typo="Typo.body2" Style="font-weight: 500;">@Localizer["Choose Action"]:</MudText>
                                <MudRadioGroup @bind-Value="_isCoSignMode" Row="true">
                                    <MudRadio Value="false" Color="Color.Primary" Dense="true">@Localizer["Sign Again"]</MudRadio>
                                    <MudRadio Value="true" Color="Color.Primary" Dense="true">@Localizer["Request CoSign"]</MudRadio>
                                </MudRadioGroup>
                            </MudStack>

                            @if (_isCoSignMode)
                            {
                                <MudAlert Severity="Severity.Info" Dense="true" Class="my-2">
                                    @Localizer["SelectProviderForReview"]
                                </MudAlert>

                                <MudStack Row Spacing="3">
                                    <!-- Left side: Provider selection -->
                                    <MudItem xs="6">
                                        <MudSelect @bind-Value="_selectedReviewProvider"
                                                   Label="@Localizer["Select Provider"]"
                                                   Variant="Variant.Outlined"
                                                   Size="Size.Small"
                                                   FullWidth="true">
                                            @foreach (var provider in _providerList)
                                            {
                                                <MudSelectItem Value="@provider">@provider.UserName</MudSelectItem>
                                            }
                                        </MudSelect>
                                    </MudItem>

                                    <!-- Right side: Comments area -->
                                    <MudItem xs="6">
                                        <MudPaper Class="pa-3" Style="min-height: 150px; max-height: 300px; overflow-y: auto; background: #f8f9fa;">
                                            <MudText Typo="Typo.subtitle2" Class="mb-2" Style="font-weight: 600;">@Localizer["Comments"]</MudText>
                                            @if (ActiveReviewRequest != null && !string.IsNullOrEmpty(ActiveReviewRequest.CommentsJson) && ActiveReviewRequest.CommentsJson != "[]")
                                            {
                                                @foreach (var comment in CommentHelper.GetComments(ActiveReviewRequest.CommentsJson).Where(c => !c.IsResolved).OrderBy(c => c.CommentDate))
                                                {
                                                    <MudCard Class="comment-card mb-2" Elevation="1">
                                                        <MudCardContent Class="pa-2">
                                                            <MudText Typo="Typo.caption" Style="font-weight: 600; color: #1976d2;">
                                                                @comment.CommenterName
                                                            </MudText>
                                                            <MudText Typo="Typo.body2" Style="line-height: 1.4;">
                                                                @comment.Comment
                                                            </MudText>
                                                            <MudButton Size="Size.Small" Color="Color.Success" Variant="Variant.Text"
                                                                       OnClick="@(() => ResolveComment(comment.Id))"
                                                                       StartIcon="@Icons.Material.Filled.CheckCircle">
                                                                @Localizer["Resolve"]
                                                            </MudButton>
                                                        </MudCardContent>
                                                    </MudCard>
                                                }
                                            }
                                            else
                                            {
                                                <MudText Typo="Typo.body2" Class="text-muted">@Localizer["NoCommentsYet"]</MudText>
                                            }
                                        </MudPaper>
                                    </MudItem>
                                </MudStack>
                            }
                            else
                            {
                                <MudAlert Severity="Severity.Info" Dense="true" Class="my-2">
                                    @Localizer["Signing As"]: @CurrentUser.displayName
                                </MudAlert>
                            }
                        }
                        else
                        {
                            <!-- First time signing -->
                            <MudAlert Severity="Severity.Info" Dense="true" Class="my-2">
                                @Localizer["Signing As"]: @CurrentUser.displayName
                            </MudAlert>
                        }

                        <MudDivider />

                        <MudStack Row Justify="Justify.FlexEnd" Spacing="2">
                            <MudButton Variant="Variant.Outlined"
                                       Color="Color.Secondary"
                                       OnClick="CancelSigning"
                                       Size="Size.Small">
                                @Localizer["Cancel"]
                            </MudButton>

                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       OnClick="ProcessSignature"
                                       Size="Size.Small"
                                       StartIcon="@(_isCoSignMode ? Icons.Material.Filled.Send : Icons.Material.Filled.Draw)"
                                       Disabled="@(_isCoSignMode && _selectedReviewProvider == null)">
                                @(_isCoSignMode ? Localizer["Send"] : Localizer["Sign"])
                            </MudButton>
                        </MudStack>
                    </MudStack>
                </MudPaper>
            }

            <!-- Signature Section -->
            <MudStack Class="signature-section" Spacing="3">


                <!-- Primary Signature Display -->
                <MudTextField Variant="Variant.Outlined"
                              Value="@GetSignatureText()"
                              ReadOnly="true"
                              FullWidth="true"
                              Lines="3"
                              Multiline="true"
                              Style="font-family: monospace; background: #f8fff8;" />

                <MudStack Row Justify="Justify.FlexEnd" Spacing="2" Style="margin-top: 8px;">
                    @if (!CurrentCosigning.IsLocked)
                    {
                        <MudButton Variant="Variant.Outlined"
                                   Color="Color.Primary"
                                   StartIcon="@Icons.Material.Filled.Edit"
                                   OnClick="ShowSigningForm"
                                   Disabled="@IsProcessing"
                                   Size="Size.Small"
                                   Class="signature-button">
                            @GetSignButtonText()
                        </MudButton>
                    }

                    

                    @if (!CurrentCosigning.IsLocked && (CurrentCosigning.IsSigned) && ( CurrentCosigning.IsSigned && ActiveReviewRequest?.Status == CosigningRequestStatus.Approved))
                    {
                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Warning"
                                   StartIcon="@Icons.Material.Filled.Lock"
                                   OnClick="LockDocument"
                                   Disabled="@IsProcessing"
                                   Size="Size.Small"
                                   Class="signature-button">
                            @Localizer["Lock"]
                        </MudButton>
                    }
                </MudStack>

            </MudStack>

            <!-- Comments Section -->
            @if (ActiveReviewRequest != null)
            {
                <MudPaper Class="comments-section" Elevation="1" Style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-left: 4px solid #1976d2;">
                    <MudStack Spacing="2">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.Comment" Color="Color.Primary" Size="Size.Small" />
                            <MudText Typo="Typo.subtitle2" Style="font-weight: 600; color: #1976d2;">
                                @Localizer["ReviewComments"]
                            </MudText>
                        </MudStack>

                        <div class="comments-container" style="max-height: 300px; overflow-y: auto;">
                            @foreach (var comment in CommentHelper.GetComments(ActiveReviewRequest.CommentsJson).OrderBy(c => c.CommentDate))
                            {
                                <MudCard Class="comment-card" Elevation="1" Style="margin-bottom: 8px; background: white;">
                                    <MudCardContent Class="pa-3">
                                        <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Start" Class="mb-2">
                                            <MudText Typo="Typo.caption" Style="font-weight: 600; color: #1976d2;">
                                                @comment.CommenterName
                                            </MudText>
                                            <MudText Typo="Typo.caption" Class="text-muted">
                                                @comment.CommentDate.ToString("MM/dd/yyyy HH:mm")
                                            </MudText>
                                        </MudStack>

                                        <MudText Typo="Typo.body2" Style="line-height: 1.5; margin-bottom: 8px;">
                                            @comment.Comment
                                        </MudText>

                                        @if (comment.IsResolved)
                                        {
                                            <MudChip T="string" Color="Color.Success" Size="Size.Small" Icon="@Icons.Material.Filled.CheckCircle">
                                                @Localizer["Resolved"]
                                                @if (comment.ResolvedDate.HasValue)
                                                {
                                                    <text> - @comment.ResolvedDate.Value.ToString("MM/dd/yyyy")</text>
                                                }
                                            </MudChip>
                                        }
                                        else
                                        {
                                            <MudChip T="string" Color="Color.Warning" Size="Size.Small" Icon="@Icons.Material.Filled.Schedule">
                                                @Localizer["NeedsAttention"]
                                            </MudChip>
                                        }
                                    </MudCardContent>
                                </MudCard>
                            }
                        </div>
                    </MudStack>
                </MudPaper>
            }

            <!-- Review Request Status -->
            @if (ActiveReviewRequest != null)
            {
                <MudAlert Severity="@GetReviewRequestSeverity()"
                          Variant="Variant.Filled"
                          Dense="true"
                          Class="review-request-alert">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudIcon Icon="@GetReviewRequestIcon()" Size="Size.Small" />
                        <div>
                            <MudText Typo="Typo.body2" Style="font-weight: 600;">
                                @GetReviewRequestStatusText()
                            </MudText>
                            <MudText Typo="Typo.caption">
                                @Localizer["RequestedFrom"]: @ActiveReviewRequest.ReviewerName
                            </MudText>
                            <MudText Typo="Typo.caption">
                                @Localizer["RequestedOn"]: @ActiveReviewRequest.RequestedDate.ToString("MMM dd, yyyy 'at' h:mm tt")
                            </MudText>
                            }
                        </div>
                    </MudStack>
                </MudAlert>
            }

            <!-- Lock Status -->
            @if (CurrentCosigning.IsLocked)
            {
                <MudAlert Severity="Severity.Info"
                          Variant="Variant.Filled"
                          Dense="true"
                          Class="lock-alert">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                        <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Small" />
                        <MudText>@Localizer["DocumentLocked"]</MudText>
                    </MudStack>
                </MudAlert>
            }
        </MudPaper>
    }
</MudContainer>

<!-- Request Review Dialog -->
<MudDialog @bind-IsVisible="_showRequestReviewDialog" Options="@_dialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.RateReview" Class="mr-2" />
            @Localizer["RequestReview"]
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudStack Spacing="3">
            <MudAlert Severity="Severity.Info" Dense="true">
                @Localizer["SelectProviderForReview"]
            </MudAlert>

            <MudSelect @bind-Value="_selectedReviewProvider"
                       Label="@Localizer["SelectProvider"]"
                       Variant="Variant.Outlined"
                       FullWidth="true">
                @foreach (var provider in _providerList)
                {
                    <MudSelectItem Value="@provider">@provider.UserName</MudSelectItem>
                }
            </MudSelect>

            <MudTextField @bind-Value="_reviewRequestComment"
                          Label="@($"{Localizer["Comment"]} ({Localizer["Optional"]})")"
                          Variant="Variant.Outlined"
                          Multiline="true"
                          Lines="3"
                          FullWidth="true"
                          Placeholder="@Localizer["AddCommentForReviewer"]" />
        </MudStack>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(() => _showRequestReviewDialog = false)">
            @Localizer["Cancel"]
        </MudButton>
        <MudButton Color="Color.Primary"
                   Variant="Variant.Filled"
                   OnClick="SubmitReviewRequest"
                   Disabled="@(_selectedReviewProvider == null || IsProcessing)">
            @Localizer["SendRequest"]
        </MudButton>
    </DialogActions>
</MudDialog>

<style>
    .cosigning-container {
        padding: 0;
        margin: 0;
        width: 100%;
    }

    .cosigning-paper {
        padding: 16px;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
        background: #ffffff;
        margin-bottom: 16px;
        width: 100%;
    }

    .cosigning-header {
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f0f0f0;
    }

    .header-title {
        font-weight: 600;
        color: #1976d2;
        margin: 0;
        font-size: 1rem;
    }

    .status-chip {
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.75rem;
    }

    .signature-section {
        margin-top: 0;
    }

    .signature-block {
        padding: 16px;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
        background: #ffffff;
        transition: all 0.2s ease;
    }

        .signature-block:hover {
            border-color: #1976d2;
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
        }

        .signature-block.signed {
            background: #f8fff8;
            border-color: #4caf50;
        }

    .signature-title {
        font-weight: 600;
        color: #333;
        margin: 0;
        font-size: 0.875rem;
    }

        .signature-title.signed {
            color: #2e7d32;
        }

    .signature-button {
        min-width: 120px;
        font-weight: 500;
        text-transform: none;
        border-radius: 4px;
        font-size: 0.875rem;
    }

    .signature-divider {
        margin: 12px 0;
        opacity: 0.6;
    }

    .lock-alert {
        margin-top: 12px;
        border-radius: 4px;
    }

    .review-request-alert {
        margin-top: 12px;
        border-radius: 4px;
    }

    .comments-section {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        border-radius: 4px;
    }

    .comments-container {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .comment-card {
        border-radius: 4px;
        border: 1px solid #e0e0e0;
        transition: box-shadow 0.2s ease;
    }

        .comment-card:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

    .text-muted {
        color: #6c757d;
    }
</style>